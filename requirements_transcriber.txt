# Dependencias para Audio Transcriber
# ===================================

# Biblioteca principal para reconocimiento de voz
SpeechRecognition>=3.10.0

# Manejo y procesamiento de archivos de audio
pydub>=0.25.1

# Dependencias adicionales para Windows
# PyAudio para captura de audio (opcional, para micrófono)
# pyaudio>=0.2.11

# Bibliotecas de sistema (ya incluidas en Python estándar)
# - os
# - sys  
# - logging
# - datetime
# - time
# - pathlib
# - typing

# Notas de instalación:
# ===================
# 
# 1. Instalar dependencias básicas:
#    pip install -r requirements_transcriber.txt
#
# 2. Para Windows, puede ser necesario instalar FFmpeg:
#    - Descargar desde: https://ffmpeg.org/download.html
#    - Agregar al PATH del sistema
#    - O usar: pip install ffmpeg-python
#
# 3. Para usar PyAudio (captura de micrófono):
#    - Windows: pip install pyaudio
#    - Puede requerir Microsoft Visual C++ Build Tools
#
# 4. Alternativas avanzadas (opcional):
#    - whisper: pip install openai-whisper
#    - vosk: pip install vosk
